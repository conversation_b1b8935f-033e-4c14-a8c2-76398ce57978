#!/usr/bin/env python3
"""
Test script to run hybrid processing on a small subset before full processing.
"""

import asyncio
import csv
import json
import time
from process_full_sms_dataset import FullSMSProcessor

async def test_subset_processing():
    """Test processing on a small subset of messages."""
    
    INPUT_FILE = "sms_backup.csv"
    OUTPUT_FILE = "test_hybrid_subset_results.csv"
    SUBSET_SIZE = 100  # Test with first 100 messages
    BATCH_SIZE = 20
    
    # OpenAI API key
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    
    print("SUBSET PROCESSING TEST")
    print("="*50)
    print(f"Testing with first {SUBSET_SIZE} messages")
    print(f"Input: {INPUT_FILE}")
    print(f"Output: {OUTPUT_FILE}")
    print("="*50)
    
    # Load subset of messages
    messages = []
    with open(INPUT_FILE, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for i, row in enumerate(reader):
            if i >= SUBSET_SIZE:
                break
            # Map columns
            mapped_row = {
                'original_id': row.get('id', ''),
                'phone_number': row.get('phoneNumber', ''),
                'sender_address': row.get('senderAddress', ''),
                'timestamp': row.get('updateAt', ''),
                'original_text': row.get('text', '')
            }
            messages.append(mapped_row)
    
    print(f"Loaded {len(messages)} messages for testing")
    
    # Initialize processor
    processor = FullSMSProcessor(OPENAI_API_KEY, BATCH_SIZE)
    
    # Process messages
    start_time = time.time()
    all_results = []
    
    # Process in batches
    total_batches = (len(messages) + BATCH_SIZE - 1) // BATCH_SIZE
    
    for batch_num in range(1, total_batches + 1):
        batch_start = (batch_num - 1) * BATCH_SIZE
        batch_end = min(batch_start + BATCH_SIZE, len(messages))
        batch = messages[batch_start:batch_end]
        
        print(f"Processing batch {batch_num}/{total_batches} ({len(batch)} messages)...")
        
        try:
            batch_results = await processor.process_batch(batch, batch_num)
            all_results.extend(batch_results)
        except Exception as e:
            print(f"Error in batch {batch_num}: {e}")
    
    processing_time = time.time() - start_time
    
    # Save results
    processor.save_results(all_results, OUTPUT_FILE)
    
    # Analyze results
    print("\n" + "="*50)
    print("TEST RESULTS SUMMARY")
    print("="*50)
    
    total = len(all_results)
    financial = sum(1 for r in all_results if r.get('classification') == 'financial')
    non_financial = sum(1 for r in all_results if r.get('classification') == 'non-financial')
    errors = sum(1 for r in all_results if r.get('processing_status') == 'error')
    
    # Count OpenAI calls
    openai_calls = 0
    for result in all_results:
        if result.get('classification') == 'financial':
            extracted_data = result.get('extracted_data_json', '{}')
            if extracted_data != '{}':
                try:
                    data = json.loads(extracted_data)
                    if len(data) > 5:  # Rich extraction indicates OpenAI
                        openai_calls += 1
                except:
                    pass
    
    print(f"Total processed: {total}")
    print(f"Financial: {financial} ({financial/total*100:.1f}%)")
    print(f"Non-financial: {non_financial} ({non_financial/total*100:.1f}%)")
    print(f"Errors: {errors} ({errors/total*100:.1f}%)")
    print(f"OpenAI calls: {openai_calls} ({openai_calls/total*100:.1f}%)")
    print(f"Processing time: {processing_time:.1f}s")
    print(f"Rate: {total/processing_time:.1f} messages/second")
    
    # Estimate full dataset
    total_messages = 5174  # From wc -l
    estimated_time = (total_messages / total) * processing_time
    estimated_openai_calls = (openai_calls / total) * total_messages
    estimated_cost = estimated_openai_calls * 0.002
    
    print(f"\nFULL DATASET ESTIMATES:")
    print(f"Estimated processing time: {estimated_time/60:.1f} minutes")
    print(f"Estimated OpenAI calls: {estimated_openai_calls:.0f}")
    print(f"Estimated cost: ${estimated_cost:.2f}")
    
    # Show sample results
    print(f"\nSAMPLE FINANCIAL EXTRACTIONS:")
    financial_samples = [r for r in all_results if r.get('classification') == 'financial'][:3]
    
    for i, sample in enumerate(financial_samples, 1):
        print(f"\n{i}. {sample.get('sender_address', 'Unknown')}")
        print(f"   Text: {sample.get('original_text', '')[:60]}...")
        print(f"   Amount: {sample.get('amount', 'N/A')}")
        print(f"   Entity: {sample.get('entity_name', 'N/A')}")
        print(f"   Account: {sample.get('account_number', 'N/A')}")
    
    print(f"\n✅ Test completed successfully!")
    print(f"📄 Results saved to: {OUTPUT_FILE}")
    
    return all_results

if __name__ == "__main__":
    asyncio.run(test_subset_processing())
