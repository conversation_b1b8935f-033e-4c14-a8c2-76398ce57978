import asyncio
import json
import csv
import os
from typing import List, Dict, Optional
import time

from langchain_openai import ChatOpenAI
from langchain.prompts import ChatPromptTemplate
from pydantic import BaseModel, Field

# Import existing classification logic
from classifiers import SMSClassifier
from field_extractors import FieldExtractor
from utils import clean_text

class HybridSMSProcessor:
    """
    Hybrid SMS processor that combines rule-based financial/non-financial classification
    with OpenAI-based field extraction for financial messages only.
    """
    
    def __init__(self, openai_api_key: str = None):
        """Initialize the hybrid processor."""
        self.classifier = SMSClassifier()
        self.extractor = FieldExtractor()
        
        # Initialize OpenAI LLM if API key is provided
        if openai_api_key:
            os.environ["OPENAI_API_KEY"] = openai_api_key
            try:
                self.llm = ChatOpenAI(model="gpt-4o-mini", temperature=0)
                self.use_openai = True
            except Exception as e:
                print(f"Warning: Could not initialize OpenAI LLM: {e}")
                self.llm = None
                self.use_openai = False
        else:
            self.llm = None
            self.use_openai = False
        
        # Load type-field mapping for OpenAI extraction
        self.type_field_mapping = self._load_type_field_mapping()
    
    def _load_type_field_mapping(self) -> Dict:
        """Load the type-field mapping from JSON file."""
        try:
            with open("type_fieldname_mapping.json", "r", encoding="utf-8") as f:
                return json.load(f)
        except FileNotFoundError:
            print("Warning: 'type_fieldname_mapping.json' not found. OpenAI extraction will be limited.")
            return {}
        except json.JSONDecodeError:
            print("Warning: Could not decode JSON from 'type_fieldname_mapping.json'.")
            return {}
    
    async def process_sms(self, sms_data: Dict) -> Dict:
        """
        Process a single SMS message using hybrid approach.
        
        Args:
            sms_data: Dictionary containing SMS data with keys like 'original_text', 'phone_number', etc.
        
        Returns:
            Dictionary with processed results
        """
        original_text = sms_data.get('original_text', '')
        
        # Initialize result with original data
        result = {
            'original_id': sms_data.get('original_id', ''),
            'phone_number': sms_data.get('phone_number', ''),
            'sender_address': sms_data.get('sender_address', ''),
            'timestamp': sms_data.get('timestamp', ''),
            'original_text': original_text,
            'processing_status': 'success',
            'error_message': ''
        }
        
        try:
            # Step 1: Clean the SMS text
            cleaned_text = clean_text(original_text)
            
            # Step 2: Use rule-based classification to determine if financial or non-financial
            initial_classification = self.classifier.classify_sms(cleaned_text)
            
            # Step 3: Extract amount using existing extractor to validate classification
            extracted_amount = await self.extractor.extract_amount(cleaned_text)
            
            # Step 4: Validate classification based on amount extraction
            validated_classification = self.classifier.validate_financial_classification(
                initial_classification, extracted_amount
            )
            
            # Step 5: Determine if this is a financial message
            is_financial = (
                validated_classification['sms_type'] != 'Other' or 
                validated_classification['sms_event_subtype'] != 'Non-Financial'
            )
            
            if not is_financial:
                # Non-financial message - use rule-based results only
                result.update({
                    'classification': 'non-financial',
                    'sms_type': 'Other',
                    'sms_event_subtype': 'Non-Financial',
                    'sms_info_type': 'Other',
                    'extracted_data_json': '{}',
                    'amount': '',
                    'date': '',
                    'account_number': '',
                    'bank_name': '',
                    'txn_ref': '',
                    'currency': '',
                    # OpenAI fields
                    'classified_type': 'Non Financial',
                    'classified_subtype': None,
                    'classified_infotype': None,
                    'entity_name': None
                })
            else:
                # Financial message - use OpenAI for detailed extraction if available
                if self.use_openai and self.type_field_mapping:
                    openai_result = await self._extract_with_openai(sms_data, validated_classification)
                    result.update(openai_result)
                else:
                    # Fallback to rule-based extraction
                    rule_based_result = await self._extract_with_rules(cleaned_text, validated_classification)
                    result.update(rule_based_result)
                    
        except Exception as e:
            result.update({
                'processing_status': 'error',
                'error_message': str(e),
                'classification': 'error',
                'sms_type': 'Error',
                'sms_event_subtype': 'Processing Error',
                'sms_info_type': 'Error'
            })
        
        return result
    
    async def _extract_with_openai(self, sms_data: Dict, rule_classification: Dict) -> Dict:
        """Extract information using OpenAI for financial messages."""
        # Map rule-based classification to OpenAI types
        openai_type = self._map_rule_to_openai_type(rule_classification)
        
        # Use OpenAI extraction
        from openai_label_generation import extract_information
        
        openai_extraction = await extract_information(
            self.llm, 
            {'body': sms_data.get('original_text', '')}, 
            openai_type, 
            self.type_field_mapping
        )
        
        # Also get rule-based extraction for comparison/fallback
        rule_extraction = await self._extract_with_rules(
            clean_text(sms_data.get('original_text', '')), 
            rule_classification
        )
        
        # Combine results, preferring OpenAI but falling back to rules
        result = {
            'classification': 'financial',
            'sms_type': rule_classification['sms_type'],
            'sms_event_subtype': rule_classification['sms_event_subtype'],
            'sms_info_type': rule_classification['sms_info_type'],
            'classified_type': openai_type,
            'classified_subtype': None,  # OpenAI doesn't use subtype in same way
            'classified_infotype': None,  # OpenAI doesn't use infotype in same way
            
            # Prefer OpenAI extraction, fallback to rule-based
            'entity_name': openai_extraction.get('entity_name') or rule_extraction.get('bank_name', ''),
            'amount': openai_extraction.get('transaction_amount') or rule_extraction.get('amount', ''),
            'currency': openai_extraction.get('currency', 'INR'),
            'account_number': openai_extraction.get('account_number') or rule_extraction.get('account_number', ''),
            'txn_ref': openai_extraction.get('txn_ref') or rule_extraction.get('txn_ref', ''),
            'date': rule_extraction.get('date', ''),  # Rule-based is better for dates
            'bank_name': openai_extraction.get('entity_name') or rule_extraction.get('bank_name', ''),
            
            # Store full OpenAI extraction as JSON
            'extracted_data_json': json.dumps(openai_extraction)
        }
        
        # Add any additional OpenAI fields
        for field_name, field_value in openai_extraction.items():
            if field_name not in result and field_value is not None:
                result[field_name] = field_value
        
        return result
    
    async def _extract_with_rules(self, cleaned_text: str, classification: Dict) -> Dict:
        """Extract information using rule-based approach."""
        # Use existing field extractors
        amount = await self.extractor.extract_amount(cleaned_text)
        date = await self.extractor.extract_date(cleaned_text)
        account_number = await self.extractor.extract_account_number(cleaned_text)
        bank_name = await self.extractor.extract_bank_name(cleaned_text)
        txn_ref = await self.extractor.extract_transaction_reference(cleaned_text)
        
        return {
            'classification': 'financial',
            'sms_type': classification['sms_type'],
            'sms_event_subtype': classification['sms_event_subtype'],
            'sms_info_type': classification['sms_info_type'],
            'classified_type': self._map_rule_to_openai_type(classification),
            'classified_subtype': None,
            'classified_infotype': None,
            'extracted_data_json': json.dumps({
                'amount': amount,
                'date': date,
                'account_number': account_number,
                'bank_name': bank_name,
                'txn_ref': txn_ref
            }),
            'amount': amount or '',
            'date': date or '',
            'account_number': account_number or '',
            'bank_name': bank_name or '',
            'txn_ref': txn_ref or '',
            'currency': 'INR',
            'entity_name': bank_name or ''
        }
    
    def _map_rule_to_openai_type(self, rule_classification: Dict) -> str:
        """Map rule-based classification to OpenAI type."""
        rule_type = rule_classification.get('sms_type', 'Other')
        
        mapping = {
            'Purchase': 'Purchase',
            'Payment': 'Payment', 
            'Deposit & Withdrawal': 'Deposit & Withdrawal',
            'Accounts': 'Accounts',
            'Money Transfer': 'Money Transfer',
            'Investment': 'Investment',
            'Other': 'Non Financial'
        }
        
        return mapping.get(rule_type, 'Non Financial')

async def process_csv_file(input_csv_path: str, output_csv_path: str, openai_api_key: str = None):
    """
    Process SMS messages from CSV file using hybrid approach.
    
    Args:
        input_csv_path: Path to input CSV file
        output_csv_path: Path to output CSV file
        openai_api_key: OpenAI API key (optional)
    """
    processor = HybridSMSProcessor(openai_api_key)
    
    # Read input CSV
    messages = []
    with open(input_csv_path, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        for row in reader:
            messages.append(row)
    
    print(f"Loaded {len(messages)} messages from {input_csv_path}")
    print("Starting hybrid processing (rule-based classification + OpenAI extraction)...")
    
    # Process messages in parallel
    tasks = [processor.process_sms(msg) for msg in messages]
    results = await asyncio.gather(*tasks)
    
    print(f"Processing complete. Writing {len(results)} results to {output_csv_path}")
    
    # Get all possible field names for CSV headers
    all_fields = set()
    for result in results:
        all_fields.update(result.keys())
    
    # Define standard field order
    standard_fields = [
        'original_id', 'phone_number', 'sender_address', 'timestamp', 'original_text',
        'classification', 'sms_type', 'sms_event_subtype', 'sms_info_type',
        'classified_type', 'classified_subtype', 'classified_infotype',
        'amount', 'currency', 'entity_name', 'account_number', 'bank_name', 
        'txn_ref', 'date', 'extracted_data_json',
        'processing_status', 'error_message'
    ]
    
    # Add any additional fields from OpenAI extraction
    additional_fields = sorted(list(all_fields - set(standard_fields)))
    fieldnames = standard_fields + additional_fields
    
    # Write results to CSV
    with open(output_csv_path, 'w', newline='', encoding='utf-8') as f:
        writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
        writer.writeheader()
        writer.writerows(results)
    
    print(f"Successfully processed and saved results to {output_csv_path}")

if __name__ == "__main__":
    # Configuration
    INPUT_CSV = "sms_backup.csv"  # Input CSV file
    OUTPUT_CSV = "hybrid_sms_processed_results.csv"  # Output CSV file
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    
    start_time = time.time()
    print("Starting hybrid SMS processing...")
    asyncio.run(process_csv_file(INPUT_CSV, OUTPUT_CSV, OPENAI_API_KEY))
    end_time = time.time()
    print(f"Hybrid processing completed in {end_time - start_time:.2f} seconds")
