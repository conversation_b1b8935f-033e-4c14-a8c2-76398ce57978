#!/usr/bin/env python3
"""
Demo script to show hybrid SMS processing on a small sample from the actual data.
"""

import asyncio
import csv
import json
from hybrid_sms_processor import HybridSMSProcessor

async def demo_hybrid_processing():
    """Demo the hybrid processor with a small sample from actual SMS data."""
    
    # Load a small sample from the actual SMS backup
    sample_messages = []
    try:
        with open('sms_backup.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            # Take first 10 messages as demo and map column names
            for i, row in enumerate(reader):
                if i >= 10:
                    break
                # Map sms_backup.csv columns to expected format
                mapped_row = {
                    'original_id': row.get('id', f'demo{i+1}'),
                    'phone_number': row.get('phoneNumber', 'unknown'),
                    'sender_address': row.get('senderAddress', 'unknown'),
                    'timestamp': row.get('updateAt', ''),
                    'original_text': row.get('text', '')
                }
                sample_messages.append(mapped_row)
    except FileNotFoundError:
        print("sms_backup.csv not found. Using hardcoded sample messages.")
        # Fallback to hardcoded messages with real examples
        sample_messages = [
            {
                'original_id': '171448832312176',
                'phone_number': 'xx87463829',
                'sender_address': 'JD-SBIUPI',
                'timestamp': 'Apr 30, 2024 8:15:23 PM',
                'original_text': 'Dear UPI user A/C X4884 debited by 2000.0 on date 30Apr24 trf to SMAAASH Refno ************. If not u? call **********. -SBI'
            },
            {
                'original_id': '***************',
                'phone_number': 'xx87463829',
                'sender_address': 'JM-RBLBNK',
                'timestamp': 'Apr 30, 2024 9:07:36 PM',
                'original_text': 'Cash payment of Rs.2670 received on 30-04-2024 for Loan/Card (XX1772) Ref: ********************, will reflect in your account in 2 working days-RBL Bank'
            },
            {
                'original_id': '****************',
                'phone_number': 'xx87463829',
                'sender_address': 'AX-SBIUPI',
                'timestamp': 'Apr 30, 2024 9:44:24 PM',
                'original_text': 'Dear UPI user A/C X4884 debited by 135.46 on date 30Apr24 trf to MCDONALDS Refno ************. If not u? call **********. -SBI'
            },
            {
                'original_id': '****************',
                'phone_number': 'xx87463829',
                'sender_address': 'JX-JioSvc',
                'timestamp': 'Apr 30, 2024 5:59:33 PM',
                'original_text': 'For seamless data experience across the country, set the \'Data Roaming\' as On/Always. To know \'How to Set up Jio Network\' on mobile, click https://youtu.be/o18LboDi1ho To know your number, track balance & usage, give a miss call to 1299.'
            },
            {
                'original_id': '****************',
                'phone_number': 'xx87463829',
                'sender_address': 'VM-SBICRD',
                'timestamp': 'Apr 30, 2024 9:20:23 PM',
                'original_text': 'Get an Amazon Voucher worth Rs.500 with every Add-on Card. Apply before 30 June 2024 to avail offer. Apply: https://sbicard.com/addon T&C - SBI Card'
            }
        ]
    
    print("HYBRID SMS PROCESSING DEMO")
    print("=" * 60)
    print(f"Processing {len(sample_messages)} sample messages...")
    print()
    
    # Initialize processor with OpenAI
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    processor = HybridSMSProcessor(OPENAI_API_KEY)
    
    results = []
    financial_count = 0
    non_financial_count = 0
    openai_calls = 0
    
    for i, message in enumerate(sample_messages, 1):
        print(f"Message {i}: {message.get('sender_address', 'Unknown')}")
        print(f"Text: {message.get('original_text', '')[:80]}{'...' if len(message.get('original_text', '')) > 80 else ''}")
        
        try:
            result = await processor.process_sms(message)
            results.append(result)
            
            classification = result.get('classification', 'unknown')
            if classification == 'financial':
                financial_count += 1
                # Check if OpenAI was used (has rich extracted_data_json)
                extracted_data = result.get('extracted_data_json', '{}')
                if extracted_data != '{}':
                    try:
                        data = json.loads(extracted_data)
                        if len(data) > 5:  # Rich extraction indicates OpenAI was used
                            openai_calls += 1
                    except:
                        pass
            elif classification == 'non-financial':
                non_financial_count += 1
            
            print(f"  → Classification: {classification}")
            print(f"  → Type: {result.get('sms_type', 'unknown')}")
            print(f"  → Amount: {result.get('amount', 'N/A')}")
            print(f"  → Entity: {result.get('entity_name', 'N/A')}")
            
            # Show OpenAI extraction if available
            if classification == 'financial':
                extracted_data = result.get('extracted_data_json', '{}')
                if extracted_data != '{}':
                    try:
                        data = json.loads(extracted_data)
                        if len(data) > 3:  # Rich extraction
                            print(f"  → OpenAI fields: {list(data.keys())}")
                    except:
                        pass
            
        except Exception as e:
            print(f"  → ERROR: {e}")
            results.append({'original_id': message.get('original_id', f'demo{i}'), 'error': str(e)})
        
        print()
    
    # Summary
    print("=" * 60)
    print("DEMO SUMMARY")
    print("=" * 60)
    print(f"Total messages processed: {len(results)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    print(f"OpenAI API calls made: {openai_calls}")
    print(f"Cost efficiency: {openai_calls}/{len(results)} = {openai_calls/len(results)*100:.1f}% of messages used OpenAI")
    print()
    
    # Save demo results
    if results:
        # Get all field names
        all_fields = set()
        for result in results:
            all_fields.update(result.keys())
        
        fieldnames = sorted(list(all_fields))
        
        with open('demo_hybrid_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print("Demo results saved to 'demo_hybrid_results.csv'")
    
    print("\nKey Benefits Demonstrated:")
    print("✅ Fast rule-based classification for all messages")
    print("✅ OpenAI extraction only for financial messages (cost-effective)")
    print("✅ Rich field extraction for financial transactions")
    print("✅ Fallback to rule-based extraction if needed")
    print("✅ Compatible CSV output format")
    
    return results

if __name__ == "__main__":
    asyncio.run(demo_hybrid_processing())
