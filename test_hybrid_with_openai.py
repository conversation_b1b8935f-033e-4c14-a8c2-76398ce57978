#!/usr/bin/env python3
"""
Test script for the hybrid SMS processor with OpenAI integration.
"""

import asyncio
import csv
import json
from hybrid_sms_processor import HybridSMSProcessor

async def test_hybrid_with_openai():
    """Test the hybrid processor with OpenAI integration on a few financial messages."""
    
    # Initialize processor with OpenAI
    OPENAI_API_KEY = "***********************************************************************************************************************************************************************"
    processor = HybridSMSProcessor(OPENAI_API_KEY)
    
    # Test with a few financial messages only (to save API costs)
    test_messages = [
        {
            'original_id': 'openai_test1',
            'phone_number': 'xx12345678',
            'sender_address': 'SBI',
            'timestamp': '2024-01-01 12:00:00',
            'original_text': 'Rs.1000 debited from A/c X1234 on 01-Jan-24 at SBI ATM. Avl Bal: Rs.5000'
        },
        {
            'original_id': 'openai_test2',
            'phone_number': 'xx12345678',
            'sender_address': 'HDFC',
            'timestamp': '2024-01-01 13:00:00',
            'original_text': 'Rs.2500 credited to A/c X5678 on 01-Jan-24. Salary from COMPANY ABC. Bal: Rs.15000'
        },
        {
            'original_id': 'openai_test3',
            'phone_number': 'xx12345678',
            'sender_address': 'PAYTM',
            'timestamp': '2024-01-01 15:00:00',
            'original_text': 'Rs.500 paid to ZOMATO via UPI. Txn ID: *********. Wallet balance: Rs.1000'
        },
        {
            'original_id': 'openai_test4',
            'phone_number': 'xx12345678',
            'sender_address': 'JioSvc',
            'timestamp': '2024-01-01 14:00:00',
            'original_text': 'Your Jio number is ready for use. Download MyJio app for best experience.'
        }
    ]
    
    print("Testing Hybrid SMS Processor with OpenAI...")
    print("=" * 60)
    
    results = []
    for i, message in enumerate(test_messages, 1):
        print(f"\nTest {i}: Processing message from {message['sender_address']}")
        print(f"Text: {message['original_text'][:60]}{'...' if len(message['original_text']) > 60 else ''}")
        
        try:
            result = await processor.process_sms(message)
            results.append(result)
            
            print(f"Classification: {result.get('classification', 'unknown')}")
            print(f"Rule-based Type: {result.get('sms_type', 'unknown')}")
            print(f"OpenAI Type: {result.get('classified_type', 'unknown')}")
            print(f"Amount: {result.get('amount', 'N/A')}")
            print(f"Entity: {result.get('entity_name', 'N/A')}")
            print(f"Status: {result.get('processing_status', 'unknown')}")
            
            # Show OpenAI extraction details
            extracted_data = result.get('extracted_data_json', '{}')
            if extracted_data != '{}':
                try:
                    data = json.loads(extracted_data)
                    print(f"OpenAI Extraction: {data}")
                except:
                    print(f"OpenAI Extraction: {extracted_data}")
            
            if result.get('error_message'):
                print(f"Error: {result['error_message']}")
                
        except Exception as e:
            print(f"ERROR processing message: {e}")
            results.append({
                'original_id': message['original_id'],
                'error': str(e)
            })
    
    print("\n" + "=" * 60)
    print("OpenAI Test Summary:")
    
    financial_count = sum(1 for r in results if r.get('classification') == 'financial')
    non_financial_count = sum(1 for r in results if r.get('classification') == 'non-financial')
    error_count = sum(1 for r in results if r.get('processing_status') == 'error' or 'error' in r)
    
    print(f"Total messages processed: {len(results)}")
    print(f"Financial messages: {financial_count}")
    print(f"Non-financial messages: {non_financial_count}")
    print(f"Errors: {error_count}")
    
    # Save test results to CSV
    if results:
        # Get all field names
        all_fields = set()
        for result in results:
            all_fields.update(result.keys())
        
        fieldnames = sorted(list(all_fields))
        
        with open('test_hybrid_openai_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.DictWriter(f, fieldnames=fieldnames, extrasaction='ignore')
            writer.writeheader()
            writer.writerows(results)
        
        print(f"\nOpenAI test results saved to 'test_hybrid_openai_results.csv'")
    
    return results

if __name__ == "__main__":
    asyncio.run(test_hybrid_with_openai())
